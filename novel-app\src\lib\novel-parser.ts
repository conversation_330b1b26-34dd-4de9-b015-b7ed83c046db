import fs from 'fs';
import path from 'path';
import { novelDb, chapterDb, Novel, Chapter } from './database';
import { fileManager } from './file-manager';

// 小说解析配置
interface ParseConfig {
  chapterPattern: RegExp;
  minChapterLength: number;
  maxChapterLength: number;
}

// 分卷/分集匹配模式（用于分栏，不作为章节）
const VOLUME_PATTERNS = [
  /^\s*(?:第[一二三四五六七八九十百千万\d]+[卷集部])\s*.*$/gmi,
  /^\s*(?:[卷集部][一二三四五六七八九十百千万\d]+)\s*.*$/gmi,
];

// 章节匹配模式
const CHAPTER_PATTERNS = [
  /^\s*(?:第[一二三四五六七八九十百千万\d]+[章节回])\s*.*$/gmi,
  /^\s*(?:Chapter\s+\d+)\s*.*$/gmi,
];

// 解析小说文件
export async function parseNovelFile(filePath: string): Promise<{
  novel: Novel;
  chapters: Chapter[];
}> {
  const filename = path.basename(filePath);
  const title = filename.replace(/\.(txt|md)$/i, '');

  // 读取文件内容
  const content = fs.readFileSync(filePath, 'utf-8');

  // 创建小说记录
  const novel = novelDb.create({
    title,
    filename,
  });

  // 解析章节
  const chapters = parseChapters(content, novel.id);

  // 批量创建章节记录
  const createdChapters = chapterDb.createBatch(chapters);

  // 更新小说的章节数量
  novelDb.update(novel.id, { chapterCount: createdChapters.length });

  // 保存章节文件
  await saveChapterFiles(createdChapters);

  return {
    novel: { ...novel, chapterCount: createdChapters.length },
    chapters: createdChapters,
  };
}

// 解析章节内容
function parseChapters(content: string, novelId: string): Omit<Chapter, 'id' | 'createdAt'>[] {
  const chapters: Omit<Chapter, 'id' | 'createdAt'>[] = [];

  // 首先识别分卷/分集标记
  const volumeMatches = findVolumeMarkers(content);

  // 然后在每个分卷内或整个文本中查找章节
  if (volumeMatches.length > 0) {
    // 有分卷的情况
    console.log(`Found ${volumeMatches.length} volumes`);
    let chapterNumber = 1;

    for (let i = 0; i < volumeMatches.length; i++) {
      const volumeStart = volumeMatches[i].index;
      const volumeEnd = i + 1 < volumeMatches.length ? volumeMatches[i + 1].index : content.length;
      const volumeContent = content.slice(volumeStart, volumeEnd);

      // 在分卷内查找章节
      const volumeChapters = parseChaptersInVolume(volumeContent, novelId, chapterNumber, volumeMatches[i].title);
      chapters.push(...volumeChapters);
      chapterNumber += volumeChapters.length;
    }
  } else {
    // 没有分卷，直接解析章节
    const directChapters = parseChaptersInVolume(content, novelId, 1);
    chapters.push(...directChapters);
  }

  console.log(`Successfully parsed ${chapters.length} chapters`);
  return chapters;
}

// 查找分卷标记
function findVolumeMarkers(content: string): Array<{ index: number; title: string }> {
  const volumeMarkers: Array<{ index: number; title: string }> = [];

  for (const pattern of VOLUME_PATTERNS) {
    const matches = Array.from(content.matchAll(pattern));
    for (const match of matches) {
      volumeMarkers.push({
        index: match.index!,
        title: extractChapterTitle(match[0])
      });
    }
  }

  // 按位置排序
  return volumeMarkers.sort((a, b) => a.index - b.index);
}

// 在指定内容中解析章节（重构版）
function parseChaptersInVolume(
  content: string,
  novelId: string,
  startChapterNumber: number,
  volumeTitle?: string
): Omit<Chapter, 'id' | 'createdAt'>[] {
  const chapters: Omit<Chapter, 'id' | 'createdAt'>[] = [];
  let chapterNumber = startChapterNumber;

  // 1. 寻找最佳匹配模式
  let bestPattern: RegExp | null = null;
  let bestMatchesCount = -1;

  for (const pattern of CHAPTER_PATTERNS) {
    const matches = content.match(pattern); // 使用 match 而不是 matchAll 来计数
    const matchCount = matches ? matches.length : 0;
    if (matchCount > bestMatchesCount) {
      bestMatchesCount = matchCount;
      bestPattern = pattern;
    }
  }

  // 2. 如果没有找到任何章节标记，将整个内容作为一章
  if (!bestPattern || bestMatchesCount === 0) {
    const trimmedContent = content.trim();
    if (trimmedContent.length > 100) { // 只有内容足够长才作为章节
      chapters.push({
        novelId,
        chapterNumber: chapterNumber,
        title: volumeTitle || '全文',
        content: trimmedContent,
        filename: `chapter_${chapterNumber}.txt`,
      });
    }
    return chapters;
  }

  // 3. 使用 split 进行分割 (关键改动)
  // 创建一个带捕获组的新正则表达式，以便 split 保留分隔符
  const splitPattern = new RegExp(`(${bestPattern.source})`, 'gmi');
  const parts = content.split(splitPattern);

  // parts 数组的结构会是: [前言部分, 标题1, 内容1, 标题2, 内容2, ...]

  let currentContent = '';

  // 处理可能存在的前言/序章（parts[0]）
  const prologue = parts[0]?.trim();
  if (prologue && prologue.length > 100) {
    chapters.push({
      novelId,
      chapterNumber: chapterNumber,
      // 你可以给它一个固定的名字，或者尝试从内容中提取
      title: '序章',
      content: prologue,
      filename: `chapter_${chapterNumber}.txt`,
    });
    chapterNumber++;
  }

  // 4. 循环处理分割后的部分
  for (let i = 1; i < parts.length; i += 2) {

    const titlePart = parts[i];
    const contentPart = parts[i + 1] || ''; // 后面的内容部分

    if (!titlePart) continue;

    const trimmedContent = (titlePart + contentPart).trim();

    if (trimmedContent.length > 100) { // 检查章节总长度
      chapters.push({
        novelId,
        chapterNumber: chapterNumber,
        title: extractChapterTitle(titlePart), // 标题就是分割符本身
        content: trimmedContent,
        filename: `chapter_${chapterNumber}.txt`,
      });
      chapterNumber++;
    }
  }

  return chapters;
}
// 提取章节标题
function extractChapterTitle(chapterText: string): string {
  const lines = chapterText.trim().split('\n');
  const firstLine = lines[0].trim();

  // 如果第一行看起来像标题，使用它
  if (firstLine.length < 100 && firstLine.length > 0) {
    return firstLine;
  }

  // 否则尝试从前几行中找到标题
  for (let i = 0; i < Math.min(3, lines.length); i++) {
    const line = lines[i].trim();
    if (line.length > 0 && line.length < 100) {
      return line;
    }
  }

  return '未命名章节';
}

// 保存章节文件到chapters目录
async function saveChapterFiles(chapters: Chapter[]): Promise<void> {
  // 为每个小说创建子目录
  const novelIds = [...new Set(chapters.map(ch => ch.novelId))];

  for (const novelId of novelIds) {
    const novel = novelDb.getById(novelId);
    if (!novel) continue;

    const novelDir = fileManager.getNovelChaptersDir(novel.title);

    // 保存该小说的所有章节
    const novelChapters = chapters.filter(ch => ch.novelId === novelId);
    for (const chapter of novelChapters) {
      const chapterPath = path.join(novelDir, chapter.filename);
      fileManager.writeFile(chapterPath, chapter.content);
    }
  }
}

// 获取novels目录中的所有小说文件
export function getAvailableNovels(): string[] {
  const novelsDir = fileManager.getNovelsDir();
  return fileManager.listFiles(novelsDir, ['.txt', '.md']);
}

// 检查小说是否已经被解析
export function isNovelParsed(filename: string): boolean {
  const novels = novelDb.getAll();
  return novels.some(novel => novel.filename === filename);
}

// 重新解析小说（删除旧数据并重新解析）
export async function reparseNovel(filename: string): Promise<{
  novel: Novel;
  chapters: Chapter[];
} | null> {
  const novelsDir = fileManager.getNovelsDir();
  const filePath = path.join(novelsDir, filename);

  if (!fileManager.fileExists(filePath)) {
    return null;
  }

  // 删除旧的小说和章节数据
  const existingNovels = novelDb.getAll();
  const existingNovel = existingNovels.find(novel => novel.filename === filename);

  if (existingNovel) {
    chapterDb.deleteByNovelId(existingNovel.id);
    novelDb.delete(existingNovel.id);
  }

  // 重新解析
  return await parseNovelFile(filePath);
}

// 解析章节范围字符串 (例如: "1-5,7,10-12")
export function parseChapterRange(rangeStr: string, maxChapter: number): number[] {
  const chapters: number[] = [];
  const parts = rangeStr.split(',').map(part => part.trim());

  for (const part of parts) {
    if (part.includes('-')) {
      // 范围格式 (例如: "1-5")
      const [start, end] = part.split('-').map(num => parseInt(num.trim()));
      if (!isNaN(start) && !isNaN(end) && start <= end) {
        for (let i = start; i <= Math.min(end, maxChapter); i++) {
          if (i > 0 && !chapters.includes(i)) {
            chapters.push(i);
          }
        }
      }
    } else {
      // 单个章节
      const chapterNum = parseInt(part);
      if (!isNaN(chapterNum) && chapterNum > 0 && chapterNum <= maxChapter && !chapters.includes(chapterNum)) {
        chapters.push(chapterNum);
      }
    }
  }

  return chapters.sort((a, b) => a - b);
}
