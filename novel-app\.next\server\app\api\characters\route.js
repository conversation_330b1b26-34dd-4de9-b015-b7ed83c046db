var R=require("../../../chunks/[turbopack]_runtime.js")("server/app/api/characters/route.js")
R.c("server/chunks/node_modules_next_9c19fe2b._.js")
R.c("server/chunks/[root-of-the-server]__6247a8a0._.js")
R.m("[project]/.next-internal/server/app/api/characters/route/actions.js [app-rsc] (server actions loader, ecmascript)")
R.m("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/src/app/api/characters/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)")
module.exports=R.m("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/src/app/api/characters/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)").exports
